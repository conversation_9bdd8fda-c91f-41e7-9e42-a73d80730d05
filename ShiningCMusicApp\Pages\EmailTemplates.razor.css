/* Email Templates Page Styles */

.email-template-modal .e-dialog {
    max-width: 95vw;
    max-height: 95vh;
}

.email-template-modal .e-dialog-content {
    padding: 20px;
    overflow-y: auto;
}

/* Rich Text Editor Styling */
.e-richtexteditor {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.e-richtexteditor .e-rte-content {
    min-height: 300px;
}

.e-richtexteditor .e-toolbar {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* Template content badges */
.badge {
    font-size: 0.75rem;
}

/* Preview modal styling */
.preview-content {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #fff;
}

/* Attachment grid styling */
.attachment-grid {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .email-template-modal .e-dialog {
        width: 95vw !important;
        height: 95vh !important;
        margin: 2.5vh auto;
    }
    
    .email-template-modal .e-dialog-content {
        padding: 15px;
    }
    
    .e-richtexteditor .e-toolbar {
        flex-wrap: wrap;
    }
    
    .e-richtexteditor .e-toolbar .e-toolbar-item {
        margin: 2px;
    }
}

/* Form styling consistency */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Button styling */
.btn-blue-custom {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-blue-custom:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
}

.btn-cancel-custom {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-cancel-custom:hover {
    background-color: #545b62;
    border-color: #545b62;
    color: white;
}

/* Grid action buttons */
.grid-action-btn {
    min-width: auto;
    padding: 0.25rem 0.5rem;
}

.grid-btn-fourth {
    width: 25%;
}

/* Template info table */
.table-sm td {
    padding: 0.25rem 0.5rem;
    vertical-align: top;
}

/* Placeholder text styling */
.text-muted {
    color: #6c757d !important;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Rich text editor custom styling */
.custom-rte {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.custom-rte:focus-within {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Attachment section styling */
.attachment-section {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    background-color: white;
}

.attachment-item:last-child {
    margin-bottom: 0;
}

/* Preview section styling */
.preview-section {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background-color: white;
}

.preview-html {
    font-family: Arial, sans-serif;
    line-height: 1.6;
}

.preview-text {
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    font-size: 0.9rem;
}

/* Modal header styling */
.modal-header-custom {
    background-color: #007bff;
    color: white;
    border-bottom: 1px solid #dee2e6;
}

/* Validation styling */
.validation-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success/Error message styling */
.alert-custom {
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
}

.alert-success-custom {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-error-custom {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Responsive grid columns */
@media (max-width: 576px) {
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell {
        padding: 4px;
        font-size: 0.875rem;
    }

    .mobile-grid .e-grid .e-gridheader .e-table .e-headercelldiv {
        font-size: 0.875rem;
        padding: 4px;
    }
}

/* Tablet-specific responsive adjustments for Galaxy Tab S7 Plus and similar devices */
@media (min-width: 577px) and (max-width: 1200px) {
    /* Force actions column to be narrower on tablets to prevent horizontal scrolling */
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell:last-child,
    .mobile-grid .e-grid .e-gridheader .e-table .e-headercell:last-child {
        max-width: 200px !important;
        width: 200px !important;
    }

    /* Adjust CC and BCC columns for tablets */
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell:nth-child(3),
    .mobile-grid .e-grid .e-gridheader .e-table .e-headercell:nth-child(3),
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell:nth-child(4),
    .mobile-grid .e-grid .e-gridheader .e-table .e-headercell:nth-child(4) {
        max-width: 100px !important;
        width: 100px !important;
    }

    /* Ensure action buttons remain readable on tablets */
    .grid-action-btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }

    /* Hide text labels on tablets to save space */
    .grid-action-btn .d-none.d-lg-inline {
        display: none !important;
    }
}

/* Additional media query for larger tablets like Galaxy Tab S7 Plus in landscape */
@media (min-width: 1201px) and (max-width: 1800px) and (orientation: landscape) {
    /* Force actions column to be narrower on large tablets to prevent horizontal scrolling */
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell:last-child,
    .mobile-grid .e-grid .e-gridheader .e-table .e-headercell:last-child {
        max-width: 200px !important;
        width: 200px !important;
    }

    /* Adjust CC and BCC columns for large tablets */
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell:nth-child(3),
    .mobile-grid .e-grid .e-gridheader .e-table .e-headercell:nth-child(3),
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell:nth-child(4),
    .mobile-grid .e-grid .e-gridheader .e-table .e-headercell:nth-child(4) {
        max-width: 100px !important;
        width: 100px !important;
    }
}

/* Specific media query for Galaxy Tab S7 Plus and similar high-resolution tablets */
@media (min-width: 1700px) and (max-width: 1900px) and (max-height: 1200px) {
    /* Force actions column to be narrower to prevent horizontal scrolling */
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell:last-child,
    .mobile-grid .e-grid .e-gridheader .e-table .e-headercell:last-child {
        max-width: 200px !important;
        width: 200px !important;
    }

    /* Adjust CC and BCC columns */
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell:nth-child(3),
    .mobile-grid .e-grid .e-gridheader .e-table .e-headercell:nth-child(3),
    .mobile-grid .e-grid .e-gridcontent .e-table .e-row .e-rowcell:nth-child(4),
    .mobile-grid .e-grid .e-gridheader .e-table .e-headercell:nth-child(4) {
        max-width: 100px !important;
        width: 100px !important;
    }
}

/* Toolbar customization for mobile */
@media (max-width: 768px) {
    .e-richtexteditor .e-rte-toolbar .e-toolbar-items {
        flex-wrap: wrap;
    }
    
    .e-richtexteditor .e-rte-toolbar .e-toolbar-item {
        margin: 1px;
    }
    
    .e-richtexteditor .e-rte-toolbar .e-separator {
        display: none;
    }
}
