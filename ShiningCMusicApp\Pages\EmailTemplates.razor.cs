using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicCommon.Models;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;
using Syncfusion.Blazor.RichTextEditor;

namespace ShiningCMusicApp.Pages;

public partial class EmailTemplatesBase : ComponentBase
{
    [Inject] protected IEmailTemplateApiService EmailTemplateApi { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected IDialogService DialogService { get; set; } = default!;

    // Data properties
    protected List<EmailTemplate> templates = new();
    protected List<EmailAttachment> currentAttachments = new();
    protected bool isLoading = true;
    protected bool showModal = false;
    protected bool showAttachmentsModal = false;
    protected bool showAddAttachmentModal = false;
    protected bool showPreviewModal = false;
    protected bool isEditMode = false;
    protected bool isSaving = false;
    protected bool isSavingAttachment = false;
    protected string modalTitle = string.Empty;
    protected string attachmentsModalTitle = string.Empty;
    protected string previewModalTitle = string.Empty;
    protected EmailTemplate currentTemplate = new();
    protected EmailTemplate? previewTemplate = null;
    protected EmailAttachment currentAttachment = new();
    protected string currentTemplateName = string.Empty;
    protected string gridKey = Guid.NewGuid().ToString();

    // Rich Text Editor toolbar configuration
    protected List<ToolbarItemModel> toolbarItems = new()
    {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.FontColor },
        new ToolbarItemModel() { Command = ToolbarCommand.BackgroundColor },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Formats },
        new ToolbarItemModel() { Command = ToolbarCommand.Alignments },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
        new ToolbarItemModel() { Command = ToolbarCommand.Image },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Indent },
        new ToolbarItemModel() { Command = ToolbarCommand.Outdent },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.SourceCode }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    protected async Task LoadData()
    {
        isLoading = true;
        try
        {
            templates = await EmailTemplateApi.GetTemplatesAsync();
            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {templates.Count} email templates");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await DialogService.ShowErrorAsync("Error loading data", ex.Message);
        }
        finally
        {
            isLoading = false;
        }
    }

    protected async Task RefreshData()
    {
        await LoadData();
    }

    protected void OpenCreateModal()
    {
        currentTemplate = new EmailTemplate();
        isEditMode = false;
        modalTitle = "Create New Email Template";
        showModal = true;
    }

    protected void OpenEditModal(EmailTemplate? template)
    {
        if (template != null)
        {
            currentTemplate = new EmailTemplate
            {
                Name = template.Name,
                Subject = template.Subject,
                CcEmailAddresses = template.CcEmailAddresses,
                BccEmailAddresses = template.BccEmailAddresses,
                BodyText = template.BodyText,
                BodyHtml = template.BodyHtml
            };

            isEditMode = true;
            modalTitle = "Edit Email Template";
            showModal = true;
        }
    }

    protected void CloseModal()
    {
        showModal = false;
        currentTemplate = new();
        isSaving = false;
    }

    protected async Task SaveTemplate()
    {
        if (string.IsNullOrWhiteSpace(currentTemplate.Name))
        {
            await DialogService.ShowWarningAsync("Template name is required.", "Please enter a valid template name before saving.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditMode)
            {
                success = await EmailTemplateApi.UpdateTemplateAsync(currentTemplate.Name, currentTemplate);
            }
            else
            {
                var createdTemplate = await EmailTemplateApi.CreateTemplateAsync(currentTemplate);
                success = createdTemplate != null;
            }

            if (success)
            {
                CloseModal();
                await LoadData();
                await DialogService.ShowSuccessAsync("Template saved successfully!");
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save template", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving template: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving template", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task DeleteTemplate(EmailTemplate? template)
    {
        if (template == null) return;

        try
        {
            var message = $"Are you sure you want to delete the email template '{template.Name}'?";
            var details = "This action cannot be undone. All attachments will also be deleted.";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete Email Template");

            if (confirmed)
            {
                var success = await EmailTemplateApi.DeleteTemplateAsync(template.Name);
                if (success)
                {
                    await LoadData();
                    await DialogService.ShowSuccessAsync("Template deleted successfully!");
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete template", "Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting template: {ex.Message}");
            await DialogService.ShowErrorAsync("Error deleting template", ex.Message);
        }
    }

    protected async Task ManageAttachments(EmailTemplate? template)
    {
        if (template == null) return;

        try
        {
            currentTemplateName = template.Name;
            attachmentsModalTitle = $"Manage Attachments - {template.Name}";
            currentAttachments = await EmailTemplateApi.GetTemplateAttachmentsAsync(template.Name);
            showAttachmentsModal = true;
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading attachments: {ex.Message}");
            await DialogService.ShowErrorAsync("Error loading attachments", ex.Message);
        }
    }

    protected void OpenAddAttachmentModal()
    {
        currentAttachment = new EmailAttachment
        {
            TemplateName = currentTemplateName
        };
        showAddAttachmentModal = true;
    }

    protected void CloseAddAttachmentModal()
    {
        showAddAttachmentModal = false;
        currentAttachment = new();
        isSavingAttachment = false;
    }

    protected async Task SaveAttachment()
    {
        if (string.IsNullOrWhiteSpace(currentAttachment.AttachmentName) || 
            string.IsNullOrWhiteSpace(currentAttachment.AttachmentPath))
        {
            await DialogService.ShowWarningAsync("All fields are required.", "Please fill in all attachment details.");
            return;
        }

        isSavingAttachment = true;
        try
        {
            var createdAttachment = await EmailTemplateApi.AddAttachmentAsync(currentAttachment);
            if (createdAttachment != null)
            {
                CloseAddAttachmentModal();
                currentAttachments = await EmailTemplateApi.GetTemplateAttachmentsAsync(currentTemplateName);
                await DialogService.ShowSuccessAsync("Attachment added successfully!");
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to add attachment", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving attachment: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving attachment", ex.Message);
        }
        finally
        {
            isSavingAttachment = false;
        }
    }

    protected async Task DeleteAttachment(EmailAttachment? attachment)
    {
        if (attachment == null) return;

        try
        {
            var message = $"Are you sure you want to delete the attachment '{attachment.AttachmentName}'?";
            var details = "This action cannot be undone.";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete Attachment");

            if (confirmed)
            {
                var success = await EmailTemplateApi.DeleteAttachmentAsync(attachment.ID);
                if (success)
                {
                    currentAttachments = await EmailTemplateApi.GetTemplateAttachmentsAsync(currentTemplateName);
                    await DialogService.ShowSuccessAsync("Attachment deleted successfully!");
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete attachment", "Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting attachment: {ex.Message}");
            await DialogService.ShowErrorAsync("Error deleting attachment", ex.Message);
        }
    }

    protected void PreviewTemplate(EmailTemplate? template)
    {
        if (template != null)
        {
            previewTemplate = template;
            previewModalTitle = $"Preview - {template.Name}";
            showPreviewModal = true;
        }
    }

}
